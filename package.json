{"name": "vscode-web-components-ai", "version": "0.0.3", "displayName": "Web Component AI Tools", "description": "Supercharge your AI coding assistants with web component information from your workspace and dependencies. Generate accurate component code using your actual custom elements, properties, and APIs.", "publisher": "d13", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN LICENSE", "categories": ["AI", "Other"], "homepage": "https://github.com/d13/vscode-web-components-ai", "bugs": {"url": "https://github.com/d13/vscode-web-components-ai/issues"}, "repository": {"url": "https://github.com/d13/vscode-web-components-ai.git", "type": "git"}, "galleryBanner": {"color": "#181c21", "theme": "dark"}, "icon": "images/wcai-icon.png", "engines": {"node": "^20.18.3", "vscode": "^1.99.0"}, "preview": true, "capabilities": {"virtualWorkspaces": false}, "activationEvents": ["workspaceContains:**/custom-elements.json", "workspaceContains:**/package.json"], "contributes": {"configuration": [{"id": "general", "title": "General", "order": 0, "properties": {"wcai.outputLevel": {"type": "string", "default": "warn", "enum": ["off", "error", "warn", "info", "debug"], "enumDescriptions": ["Logs nothing", "Logs only errors", "Logs errors and warnings", "Logs errors, warnings, and messages", "Logs verbose errors, warnings, and messages. Best for issue reporting."], "markdownDescription": "Specifies how much (if any) output will be sent to the Web Components AI output channel", "scope": "window", "order": 10}}}, {"id": "manifests", "title": "Custom Elements Manifests", "order": 5, "properties": {"wcai.manifests.exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "List of manifest file URIs to exclude from processing and display.", "scope": "window", "order": 10}}}, {"id": "mcp", "title": "Model Context Protocol (MCP) Server", "order": 10, "properties": {"wcai.mcp.port": {"type": "number", "default": 0, "description": "The HTTP port for the MCP server. The OS will assign a port if this is not set and likely different each time.", "scope": "window", "order": 10}, "wcai.mcp.host": {"type": "string", "default": "127.0.0.1", "description": "The HTTP host for the MCP server.", "scope": "window", "order": 20}, "wcai.mcp.storeHostAndPortOnStart": {"type": "boolean", "default": true, "description": "Saves the host and port of the MCP server after startup if they are not already in settings. This prevents MCP configuration files from needing to be updated after the MCP server is restarted.", "scope": "window", "order": 30}}}], "commands": [{"command": "wcai.manifests.list", "title": "List Custom Elements Manifests", "category": "Web Components AI"}, {"command": "wcai.manifests.locate", "title": "Locate Custom Elements Manifests", "category": "Web Components AI"}, {"command": "wcai.mcp.showInformation", "title": "MCP Server Information", "category": "Web Components AI"}, {"command": "wcai.mcp.start", "title": "Start MCP Server", "category": "Web Components AI"}, {"command": "wcai.mcp.stop", "title": "Stop MCP Server", "category": "Web Components AI"}, {"command": "wcai.views.cemList.refresh", "title": "Refresh", "icon": "$(refresh)"}, {"command": "wcai.views.cemList.include", "title": "Include Manifest", "icon": "$(eye)"}, {"command": "wcai.views.cemList.exclude", "title": "Exclude <PERSON>", "icon": "$(eye-closed)"}], "viewsContainers": {"activitybar": [{"id": "web-components-ai", "title": "Web Component AI Tools", "icon": "images/wcai-icon.svg"}]}, "views": {"web-components-ai": [{"id": "wcai.views.cemList", "name": "Manifests", "type": "tree", "contextualTitle": "WCAI", "icon": "$(file-code)"}]}, "menus": {"view/title": [{"command": "wcai.views.cemList.refresh", "when": "view == wcai.views.cemList", "group": "navigation"}], "view/item/context": [{"command": "wcai.views.cemList.include", "when": "view == wcai.views.cemList && viewItem == manifestExcluded", "group": "inline"}, {"command": "wcai.views.cemList.exclude", "when": "view == wcai.views.cemList && viewItem == manifestIncluded", "group": "inline"}]}}, "main": "./dist/extension.js", "scripts": {"build": "webpack --mode development", "bundle": "webpack --mode production", "bundle:extension": "webpack --mode production --config-name extension:node", "clean": "pnpx rimraf dist out .vscode-test .vscode-test-web .eslintcache* tsconfig*.tsbuildinfo", "format": "prettier --write .", "format:check": "prettier --check .", "generate:licenses": "node ./scripts/generateLicenses.mjs", "lint": "eslint .", "lint:fix": "eslint . --fix", "package": "vsce package --no-dependencies", "package-pre": "pnpm run package --pre-release", "pub": "vsce publish --no-dependencies", "pub-pre": "vsce publish --no-dependencies --pre-release", "vscode:prepublish": "pnpm run bundle", "watch": "webpack --watch --mode development"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.3", "@wc-toolkit/cem-utilities": "^1.3.0", "custom-elements-manifest": "^2.1.0", "zod": "^3.25.32"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^22.15.17", "@types/vscode": "1.99.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vscode/vsce": "^3.5.0", "copy-webpack-plugin": "^13.0.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.32.0", "license-checker-rseidelsohn": "^4.4.2", "prettier": "^3.5.3", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "packageManager": "pnpm@10.10.0"}