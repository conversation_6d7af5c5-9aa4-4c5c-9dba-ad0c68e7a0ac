const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
  target: 'node',
  mode: 'production',
  entry: './src/extension.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'extension.js',
    libraryTarget: 'commonjs2',
    clean: true,
  },
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@wcai/shared': path.resolve(__dirname, '../shared/src'),
      '@wcai/cli': path.resolve(__dirname, '../cli/src'),
    },
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  externals: {
    vscode: 'commonjs vscode',
  },
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, '../cli/dist/main.js'),
          to: 'node_modules/@wcai/cli/dist/main.js',
        },
        {
          from: path.resolve(__dirname, 'images'),
          to: 'images',
        },
      ],
    }),
  ],
  devtool: 'source-map',
};
