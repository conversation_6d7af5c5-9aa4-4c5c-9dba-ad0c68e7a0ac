import type { Disposable } from 'vscode';
import { window } from 'vscode';
import type { ServerInfo, ServerStatus } from '@wcai/shared';
import { CliManager } from '../cli-manager';
import { configuration } from '../system/configuration';
import { Logger } from '../system/logger';
import { executeCommand } from '../system/command';

export class CliMcpProvider implements Disposable {
  private _disposables: Disposable[] = [];
  private cliManager: CliManager;
  private serverInfo: ServerInfo | undefined;

  constructor() {
    this.cliManager = new CliManager();

    this._disposables.push(
      configuration.onDidChange(async e => {
        if (e.affectsConfiguration('mcp.port') || e.affectsConfiguration('mcp.host')) {
          // Restart server if configuration changed
          await this.stop();
          void this.start();
        }
      }),
    );
  }

  getServerInfo(): ServerInfo | undefined {
    return this.serverInfo;
  }

  async start(): Promise<void> {
    if (this.serverInfo) {
      return; // Already running
    }

    Logger.log('Starting MCP server via CLI');

    try {
      this.serverInfo = await this.cliManager.startServer();
      
      if (this.serverInfo) {
        Logger.log(`MCP server started at ${this.serverInfo.url}`);
        void executeCommand('wcai.mcp.showInformation');
      } else {
        throw new Error('Failed to get server info');
      }
    } catch (error) {
      Logger.error('Failed to start MCP server', error);
      window.showErrorMessage(`Failed to start MCP server: ${error}`);
    }
  }

  async stop(): Promise<void> {
    if (!this.serverInfo) {
      return; // Not running
    }

    Logger.log('Stopping MCP server');

    try {
      await this.cliManager.stopServer();
      this.serverInfo = undefined;
      Logger.log('MCP server stopped');
    } catch (error) {
      Logger.error('Error while stopping MCP server', error);
      this.serverInfo = undefined;
    }
  }

  async getStatus(): Promise<ServerStatus> {
    try {
      const status = await this.cliManager.getServerStatus();
      
      // Update our local state
      if (status.running && status.serverInfo) {
        this.serverInfo = status.serverInfo;
      } else {
        this.serverInfo = undefined;
      }
      
      return status;
    } catch (error) {
      Logger.error('Failed to get server status', error);
      return { running: false };
    }
  }

  dispose(): void {
    void this.stop();
    this._disposables.forEach(d => d.dispose());
  }
}
