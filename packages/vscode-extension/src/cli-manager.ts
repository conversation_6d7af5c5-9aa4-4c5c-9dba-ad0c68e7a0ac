import { spawn, ChildProcess } from 'child_process';
import { workspace, window } from 'vscode';
import type { ServerInfo, ServerStatus } from '@wcai/shared';
import * as path from 'path';
import * as fs from 'fs/promises';

export class CliManager {
  private cliPath: string;

  constructor() {
    // Use bundled CLI
    this.cliPath = this.findCliPath();
  }

  private findCliPath(): string {
    // Use the bundled CLI from node_modules
    const bundledPath = path.join(__dirname, '..', 'node_modules', '@wcai', 'cli', 'dist', 'main.js');
    return bundledPath;
  }

  async startServer(): Promise<ServerInfo | undefined> {
    const workspaceRoot = workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) {
      window.showErrorMessage('No workspace folder found');
      return;
    }

    try {
      // Check if server is already running
      const status = await this.getServerStatus();
      if (status.running && status.serverInfo) {
        return status.serverInfo;
      }

      // Start CLI in daemon mode
      const process = spawn('node', [
        this.cliPath,
        'start',
        '--daemon',
        '--workspace', workspaceRoot,
        '--port', '0', // Let OS assign port
      ], {
        detached: true,
        stdio: ['ignore', 'pipe', 'pipe']
      });

      // Capture the server info from stdout
      let serverInfoText = '';
      process.stdout?.on('data', (data) => {
        serverInfoText += data.toString();
      });

      process.stderr?.on('data', (data) => {
        console.error('CLI Error:', data.toString());
      });

      // Wait for process to output server info
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Server startup timeout'));
        }, 10000);

        process.on('exit', (code) => {
          clearTimeout(timeout);
          if (code === 0 && serverInfoText) {
            try {
              const serverInfo = JSON.parse(serverInfoText.trim());
              resolve(serverInfo);
            } catch (error) {
              reject(new Error(`Failed to parse server info: ${error}`));
            }
          } else {
            reject(new Error(`CLI process exited with code ${code}`));
          }
        });

        process.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

    } catch (error) {
      window.showErrorMessage(`Failed to start MCP server: ${error}`);
      return undefined;
    }
  }

  async stopServer(): Promise<void> {
    const workspaceRoot = workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) return;

    try {
      const process = spawn('node', [
        this.cliPath,
        'stop',
        '--workspace', workspaceRoot
      ], {
        stdio: 'ignore'
      });

      await new Promise<void>((resolve, reject) => {
        process.on('exit', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`Stop command failed with code ${code}`));
          }
        });

        process.on('error', reject);
      });
    } catch (error) {
      console.error('Failed to stop server:', error);
    }
  }

  async getServerStatus(): Promise<ServerStatus> {
    const workspaceRoot = workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) {
      return { running: false };
    }

    try {
      const process = spawn('node', [
        this.cliPath,
        'status',
        '--workspace', workspaceRoot
      ], {
        stdio: ['ignore', 'pipe', 'ignore']
      });

      let output = '';
      process.stdout?.on('data', (data) => {
        output += data.toString();
      });

      return new Promise((resolve) => {
        process.on('exit', (code) => {
          if (code === 0 && output) {
            try {
              const status = JSON.parse(output.trim());
              resolve(status);
            } catch {
              resolve({ running: false });
            }
          } else {
            resolve({ running: false });
          }
        });

        process.on('error', () => {
          resolve({ running: false });
        });
      });
    } catch {
      return { running: false };
    }
  }
}
