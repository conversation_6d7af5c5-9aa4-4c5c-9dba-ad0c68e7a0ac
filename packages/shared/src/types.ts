export interface LogLevel {
  off: 'off';
  error: 'error';
  warn: 'warn';
  info: 'info';
  debug: 'debug';
}

export interface Config {
  readonly outputLevel: LogLevel[keyof LogLevel];
  readonly manifests: {
    readonly exclude: string[];
  };
  readonly mcp: {
    readonly port: number | null;
    readonly host: string | null;
    readonly storeHostAndPortOnStart: boolean;
  };
}

export interface ServerInfo {
  readonly url: string;
  readonly mcpUrl: string;
  readonly sseUrl: string;
  readonly port: number;
  readonly host: string;
}

export interface ServerStatus {
  readonly running: boolean;
  readonly pid?: number;
  readonly serverInfo?: ServerInfo;
}

export interface ComponentInfo {
  readonly tagName?: string;
  readonly className?: string;
  readonly description?: string;
  readonly attributes?: Array<{
    name: string;
    description?: string;
    type?: string;
  }>;
  readonly properties?: Array<{
    name: string;
    description?: string;
    type?: string;
  }>;
  readonly methods?: Array<{
    name: string;
    description?: string;
    parameters?: Array<{
      name: string;
      type?: string;
    }>;
  }>;
  readonly events?: Array<{
    name: string;
    description?: string;
    type?: string;
  }>;
}
