const path = require('path');

module.exports = {
  target: 'node',
  mode: 'production',
  entry: './src/main.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'main.js',
    clean: true,
  },
  resolve: {
    extensions: ['.ts', '.js'],
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  externals: {
    // Keep node modules external for CLI
    '@wcai/shared': 'commonjs @wcai/shared',
    '@modelcontextprotocol/sdk': 'commonjs @modelcontextprotocol/sdk',
    '@wc-toolkit/cem-utilities': 'commonjs @wc-toolkit/cem-utilities',
    'custom-elements-manifest': 'commonjs custom-elements-manifest',
    'zod': 'commonjs zod',
    'commander': 'commonjs commander',
    'glob': 'commonjs glob',
  },
  plugins: [],
};
