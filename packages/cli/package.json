{"name": "@wcai/cli", "version": "0.1.0", "description": "Web Component AI Tools - CLI MCP Server", "main": "./dist/index.js", "bin": {"wcai-mcp": "./dist/main.js"}, "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "dependencies": {"@wcai/shared": "workspace:*", "@modelcontextprotocol/sdk": "^1.12.3", "@wc-toolkit/cem-utilities": "^1.3.0", "custom-elements-manifest": "^2.1.0", "zod": "^3.25.32", "commander": "^12.0.0", "glob": "^11.0.0"}, "devDependencies": {"@types/node": "^22.15.17", "typescript": "^5.7.2", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "ts-loader": "^9.5.2"}}