export type LogLevel = 'off' | 'error' | 'warn' | 'info' | 'debug';

export class Logger {
  private static level: LogLevel = 'warn';

  static configure(level: LogLevel = 'warn') {
    this.level = level;
  }

  static log(message: string, ...args: any[]) {
    if (this.shouldLog('info')) {
      console.log(`[INFO] ${message}`, ...args);
    }
  }

  static warn(message: string, ...args: any[]) {
    if (this.shouldLog('warn')) {
      console.warn(`[WARN] ${message}`, ...args);
    }
  }

  static error(message: string, ...args: any[]) {
    if (this.shouldLog('error')) {
      console.error(`[ERROR] ${message}`, ...args);
    }
  }

  static debug(message: string, ...args: any[]) {
    if (this.shouldLog('debug')) {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  }

  private static shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['off', 'error', 'warn', 'info', 'debug'];
    const currentIndex = levels.indexOf(this.level);
    const messageIndex = levels.indexOf(level);
    
    return currentIndex > 0 && messageIndex <= currentIndex;
  }
}
